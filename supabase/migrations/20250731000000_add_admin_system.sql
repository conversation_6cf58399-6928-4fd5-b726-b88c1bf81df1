/*
  # 管理员系统迁移
  
  1. 为 profiles 表添加 is_admin 字段
  2. 为指定邮箱设置管理员权限
  3. 创建管理员权限相关的 RLS 策略
  4. 为 voices 表添加管理员管理策略
*/

-- 为 profiles 表添加 is_admin 字段
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS is_admin boolean DEFAULT false;

-- 为指定邮箱设置管理员权限
UPDATE profiles 
SET is_admin = true 
WHERE email = '<EMAIL>';

-- 如果管理员用户不存在，创建一个触发器在用户注册时自动设置管理员权限
CREATE OR REPLACE FUNCTION set_admin_for_specific_email()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.email = '<EMAIL>' THEN
    NEW.is_admin = true;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
DROP TRIGGER IF EXISTS set_admin_trigger ON profiles;
CREATE TRIGGER set_admin_trigger
  BEFORE INSERT ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION set_admin_for_specific_email();

-- 为 voices 表添加管理员管理策略
-- 管理员可以管理所有系统音色
CREATE POLICY "Admins can manage system voices"
  ON voices
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.is_admin = true
    )
    OR (user_id = auth.uid())
  );

-- 删除原有的用户音色策略，因为新策略已经包含了用户管理自己音色的权限
DROP POLICY IF EXISTS "Users can manage own voice models" ON voices;

-- 管理员可以查看所有用户信息（仅限管理员页面使用）
CREATE POLICY "Admins can view all profiles"
  ON profiles
  FOR SELECT
  TO authenticated
  USING (
    auth.uid() = id 
    OR EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.is_admin = true
    )
  );

-- 创建管理员专用的视图函数
CREATE OR REPLACE FUNCTION get_admin_voice_stats()
RETURNS TABLE (
  total_voices bigint,
  system_voices bigint,
  user_voices bigint,
  active_voices bigint
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_voices,
    COUNT(*) FILTER (WHERE voice_type = 'system') as system_voices,
    COUNT(*) FILTER (WHERE voice_type IN ('user_custom', 'cloned')) as user_voices,
    COUNT(*) FILTER (WHERE is_active = true) as active_voices
  FROM voices;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建管理员专用的用户统计函数
CREATE OR REPLACE FUNCTION get_admin_user_stats()
RETURNS TABLE (
  total_users bigint,
  active_users bigint,
  admin_users bigint
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_users,
    COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '30 days') as active_users,
    COUNT(*) FILTER (WHERE is_admin = true) as admin_users
  FROM profiles;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 为管理员函数设置权限
GRANT EXECUTE ON FUNCTION get_admin_voice_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION get_admin_user_stats() TO authenticated;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_profiles_is_admin ON profiles(is_admin) WHERE is_admin = true;
CREATE INDEX IF NOT EXISTS idx_voices_voice_type ON voices(voice_type);
CREATE INDEX IF NOT EXISTS idx_voices_is_active ON voices(is_active);

-- 添加注释
COMMENT ON COLUMN profiles.is_admin IS '是否为管理员用户';
COMMENT ON FUNCTION get_admin_voice_stats() IS '获取音色统计信息（管理员专用）';
COMMENT ON FUNCTION get_admin_user_stats() IS '获取用户统计信息（管理员专用）';
