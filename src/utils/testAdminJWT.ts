import { jwtDecode } from 'jwt-decode';

/**
 * 测试JWT中的管理员声明
 * 这个工具函数可以帮助调试JWT中的自定义声明
 */
export function testAdminJWT(accessToken: string) {
  try {
    const jwt = jwtDecode<any>(accessToken);
    
    console.log('=== JWT 解析结果 ===');
    console.log('完整JWT:', jwt);
    console.log('app_metadata:', jwt.app_metadata);
    console.log('admin声明:', jwt.app_metadata?.admin);
    console.log('是否为管理员:', jwt.app_metadata?.admin === 'true');
    
    return {
      isValid: true,
      isAdmin: jwt.app_metadata?.admin === 'true',
      appMetadata: jwt.app_metadata,
      fullJWT: jwt
    };
  } catch (error) {
    console.error('JWT解析失败:', error);
    return {
      isValid: false,
      isAdmin: false,
      error: error
    };
  }
}

/**
 * 在浏览器控制台中测试当前用户的JWT
 * 使用方法：在浏览器控制台中调用 window.testCurrentUserJWT()
 */
export function setupJWTTesting() {
  if (typeof window !== 'undefined') {
    (window as any).testCurrentUserJWT = () => {
      const user = JSON.parse(localStorage.getItem('sb-xfsvmyceleiafhqewdkj-auth-token') || '{}');
      if (user?.access_token) {
        return testAdminJWT(user.access_token);
      } else {
        console.log('未找到访问令牌，请先登录');
        return null;
      }
    };
    
    console.log('JWT测试工具已加载，使用 window.testCurrentUserJWT() 来测试当前用户的JWT');
  }
}
