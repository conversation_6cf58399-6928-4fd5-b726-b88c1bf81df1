import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo } from 'react';
import { User } from '@supabase/supabase-js';
import { jwtDecode } from 'jwt-decode';
import { supabase } from '../lib/supabase';
import { AuthService } from '../services/authService';
import type { Profile } from '../lib/supabase';

interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name: string) => Promise<void>;
  loginWithGitHub: () => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  isAdmin: boolean;
  updateProfile: (updates: Partial<Profile>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let mounted = true;

    // 获取初始会话
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (mounted) {
          if (session?.user) {
            setUser(session.user);
            // 异步加载用户资料，但不阻塞认证状态
            loadUserProfile(session.user.id);
          }
          setLoading(false);
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error);
        if (mounted) {
          setLoading(false);
        }
      }
    };

    getInitialSession();

    // 监听认证状态变化
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('Auth state change:', event, session?.user?.id);

        if (!mounted) return;

        if (session?.user) {
          setUser(session.user);
          // 异步加载用户资料
          loadUserProfile(session.user.id);
        } else {
          setUser(null);
          setProfile(null);
        }
        setLoading(false);
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const loadUserProfile = async (userId: string) => {
    try {
      const userProfile = await AuthService.getProfile(userId);
      setProfile(userProfile);
    } catch (error) {
      console.error('Error loading user profile:', error);
      setProfile(null);
    }
  };

  const login = async (email: string, password: string) => {
    await AuthService.signIn(email, password);
  };

  const register = async (email: string, password: string, name: string) => {
    await AuthService.signUp(email, password, name);
  };

  const loginWithGitHub = async () => {
    await AuthService.signInWithGitHub();
  };

  const logout = () => {
    AuthService.signOut();
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    if (!user) throw new Error('No user logged in');
    
    const updatedProfile = await AuthService.updateProfile(user.id, updates);
    setProfile(updatedProfile);
  };

  const isAuthenticated = user !== null;

  // 混合方式：优先从JWT读取，如果没有则从profile读取（向后兼容）
  const isAdmin = useMemo(() => {
    if (!user?.access_token) return false;

    try {
      const jwt = jwtDecode<any>(user.access_token);
      // 如果JWT中有admin声明，使用JWT中的值
      if (jwt.app_metadata?.admin !== undefined) {
        return jwt.app_metadata.admin === 'true' || jwt.app_metadata.admin === true;
      }
    } catch (error) {
      console.error('Error decoding JWT:', error);
    }

    // 如果JWT中没有admin声明，回退到profile中的is_admin字段
    return profile?.is_admin === true;
  }, [user?.access_token, profile?.is_admin]);

  return (
    <AuthContext.Provider value={{
      user,
      profile,
      loading,
      login,
      register,
      loginWithGitHub,
      logout,
      isAuthenticated,
      isAdmin,
      updateProfile
    }}>
      {children}
    </AuthContext.Provider>
  );
};