import React, { useState, useEffect } from 'react';
import { Sidebar } from '../components/layout/Sidebar';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { 
  Users, 
  Mic, 
  BarChart3, 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X,
  Eye,
  EyeOff,
  Settings
} from 'lucide-react';
import { AdminVoiceService, type AdminVoiceStats, type AdminUserStats, type CreateSystemVoiceRequest } from '../services/adminVoiceService';
import type { Voice } from '../services/voiceService';

export const AdminPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'voices' | 'users'>('overview');
  const [voiceStats, setVoiceStats] = useState<AdminVoiceStats | null>(null);
  const [userStats, setUserStats] = useState<AdminUserStats | null>(null);
  const [voices, setVoices] = useState<Voice[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 音色管理状态
  const [editingVoice, setEditingVoice] = useState<Voice | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [formData, setFormData] = useState<CreateSystemVoiceRequest>({
    name: '',
    description: '',
    uri: '',
    gender: 'female',
    language: 'zh-CN',
    preview_text: '',
    sort_order: 0,
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [voiceStatsData, userStatsData, voicesData, usersData] = await Promise.all([
        AdminVoiceService.getVoiceStats(),
        AdminVoiceService.getUserStats(),
        AdminVoiceService.getAllVoicesAdmin(),
        AdminVoiceService.getAllUsers(),
      ]);

      setVoiceStats(voiceStatsData);
      setUserStats(userStatsData);
      setVoices(voicesData);
      setUsers(usersData);
    } catch (err) {
      console.error('Error loading admin data:', err);
      setError('加载管理数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateVoice = async () => {
    try {
      setError(null);
      await AdminVoiceService.createSystemVoice(formData);
      setShowCreateForm(false);
      resetForm();
      await loadData();
    } catch (err) {
      console.error('Error creating voice:', err);
      setError('创建音色失败');
    }
  };

  const handleUpdateVoice = async () => {
    if (!editingVoice) return;

    try {
      setError(null);
      await AdminVoiceService.updateSystemVoice(editingVoice.id, formData);
      setEditingVoice(null);
      resetForm();
      await loadData();
    } catch (err) {
      console.error('Error updating voice:', err);
      setError('更新音色失败');
    }
  };

  const handleDeleteVoice = async (voiceId: string) => {
    if (!confirm('确定要删除这个音色吗？')) return;

    try {
      setError(null);
      await AdminVoiceService.deleteSystemVoice(voiceId);
      await loadData();
    } catch (err) {
      console.error('Error deleting voice:', err);
      setError('删除音色失败');
    }
  };

  const handleRestoreVoice = async (voiceId: string) => {
    try {
      setError(null);
      await AdminVoiceService.restoreSystemVoice(voiceId);
      await loadData();
    } catch (err) {
      console.error('Error restoring voice:', err);
      setError('恢复音色失败');
    }
  };

  const handleToggleUserAdmin = async (userId: string, currentStatus: boolean) => {
    if (!confirm(`确定要${currentStatus ? '取消' : '设置'}该用户的管理员权限吗？`)) return;

    try {
      setError(null);
      await AdminVoiceService.toggleUserAdmin(userId, !currentStatus);
      await loadData();
    } catch (err) {
      console.error('Error toggling user admin:', err);
      setError('修改用户权限失败');
    }
  };

  const startEdit = (voice: Voice) => {
    setEditingVoice(voice);
    setFormData({
      name: voice.name,
      description: voice.description || '',
      uri: voice.uri,
      gender: voice.gender || 'female',
      language: voice.language,
      preview_text: voice.preview_text || '',
      sort_order: voice.sort_order,
    });
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      uri: '',
      gender: 'female',
      language: 'zh-CN',
      preview_text: '',
      sort_order: 0,
    });
    setEditingVoice(null);
    setShowCreateForm(false);
  };

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-900">
        <Sidebar />
        <div className="flex-1 ml-64 p-8 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
            <p className="text-gray-400">加载管理数据中...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-900">
      <Sidebar />
      <div className="flex-1 ml-64 p-8 overflow-auto">
        <div className="max-w-7xl mx-auto">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">管理员面板</h1>
            <p className="text-gray-400">系统管理和配置</p>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
              <p className="text-red-400">{error}</p>
            </div>
          )}

          {/* 标签页导航 */}
          <div className="mb-8">
            <div className="flex space-x-1 bg-gray-800 p-1 rounded-lg w-fit">
              <button
                onClick={() => setActiveTab('overview')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'overview'
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <BarChart3 className="h-4 w-4 inline mr-2" />
                概览
              </button>
              <button
                onClick={() => setActiveTab('voices')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'voices'
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <Mic className="h-4 w-4 inline mr-2" />
                音色管理
              </button>
              <button
                onClick={() => setActiveTab('users')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'users'
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <Users className="h-4 w-4 inline mr-2" />
                用户管理
              </button>
            </div>
          </div>

          {/* 概览页面 */}
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <Card glass>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">总音色数</p>
                      <p className="text-2xl font-bold text-white">{voiceStats?.total_voices || 0}</p>
                    </div>
                    <Mic className="h-8 w-8 text-purple-400" />
                  </div>
                </div>
              </Card>

              <Card glass>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">系统音色</p>
                      <p className="text-2xl font-bold text-white">{voiceStats?.system_voices || 0}</p>
                    </div>
                    <Settings className="h-8 w-8 text-blue-400" />
                  </div>
                </div>
              </Card>

              <Card glass>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">用户音色</p>
                      <p className="text-2xl font-bold text-white">{voiceStats?.user_voices || 0}</p>
                    </div>
                    <Users className="h-8 w-8 text-green-400" />
                  </div>
                </div>
              </Card>

              <Card glass>
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">总用户数</p>
                      <p className="text-2xl font-bold text-white">{userStats?.total_users || 0}</p>
                    </div>
                    <Users className="h-8 w-8 text-orange-400" />
                  </div>
                </div>
              </Card>
            </div>
          )}

          {/* 音色管理页面 */}
          {activeTab === 'voices' && (
            <div className="space-y-6">
              {/* 创建/编辑表单 */}
              {(showCreateForm || editingVoice) && (
                <Card glass>
                  <div className="p-6">
                    <h3 className="text-lg font-semibold text-white mb-4">
                      {editingVoice ? '编辑系统音色' : '创建系统音色'}
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          音色名称 *
                        </label>
                        <input
                          type="text"
                          value={formData.name}
                          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                          placeholder="请输入音色名称"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          URI *
                        </label>
                        <input
                          type="text"
                          value={formData.uri}
                          onChange={(e) => setFormData({ ...formData, uri: e.target.value })}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                          placeholder="speech:voice_id:..."
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          性别
                        </label>
                        <select
                          value={formData.gender}
                          onChange={(e) => setFormData({ ...formData, gender: e.target.value as any })}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                        >
                          <option value="female">女性</option>
                          <option value="male">男性</option>
                          <option value="neutral">中性</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          排序顺序
                        </label>
                        <input
                          type="number"
                          value={formData.sort_order}
                          onChange={(e) => setFormData({ ...formData, sort_order: parseInt(e.target.value) || 0 })}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                          placeholder="0"
                        />
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          描述
                        </label>
                        <textarea
                          value={formData.description}
                          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                          rows={3}
                          placeholder="请输入音色描述"
                        />
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-300 mb-2">
                          预览文本
                        </label>
                        <input
                          type="text"
                          value={formData.preview_text}
                          onChange={(e) => setFormData({ ...formData, preview_text: e.target.value })}
                          className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-purple-500"
                          placeholder="请输入预览文本"
                        />
                      </div>
                    </div>

                    <div className="flex justify-end space-x-3 mt-6">
                      <Button variant="ghost" onClick={resetForm}>
                        <X className="h-4 w-4 mr-2" />
                        取消
                      </Button>
                      <Button 
                        onClick={editingVoice ? handleUpdateVoice : handleCreateVoice}
                        disabled={!formData.name || !formData.uri}
                      >
                        <Save className="h-4 w-4 mr-2" />
                        {editingVoice ? '更新' : '创建'}
                      </Button>
                    </div>
                  </div>
                </Card>
              )}

              {/* 音色列表 */}
              <Card glass>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold text-white">系统音色管理</h3>
                    <Button 
                      onClick={() => setShowCreateForm(true)} 
                      disabled={showCreateForm || editingVoice}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      添加音色
                    </Button>
                  </div>

                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-gray-700">
                          <th className="text-left py-3 px-4 text-gray-300">名称</th>
                          <th className="text-left py-3 px-4 text-gray-300">类型</th>
                          <th className="text-left py-3 px-4 text-gray-300">性别</th>
                          <th className="text-left py-3 px-4 text-gray-300">状态</th>
                          <th className="text-left py-3 px-4 text-gray-300">排序</th>
                          <th className="text-left py-3 px-4 text-gray-300">操作</th>
                        </tr>
                      </thead>
                      <tbody>
                        {voices.map((voice) => (
                          <tr key={voice.id} className="border-b border-gray-800 hover:bg-gray-800/50">
                            <td className="py-3 px-4">
                              <div>
                                <p className="text-white font-medium">{voice.name}</p>
                                {voice.description && (
                                  <p className="text-gray-400 text-sm">{voice.description}</p>
                                )}
                              </div>
                            </td>
                            <td className="py-3 px-4">
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                voice.voice_type === 'system' 
                                  ? 'bg-blue-500/20 text-blue-400' 
                                  : 'bg-green-500/20 text-green-400'
                              }`}>
                                {voice.voice_type === 'system' ? '系统' : '用户'}
                              </span>
                            </td>
                            <td className="py-3 px-4 text-gray-300">
                              {voice.gender === 'male' ? '男性' : voice.gender === 'female' ? '女性' : '中性'}
                            </td>
                            <td className="py-3 px-4">
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                voice.is_active 
                                  ? 'bg-green-500/20 text-green-400' 
                                  : 'bg-red-500/20 text-red-400'
                              }`}>
                                {voice.is_active ? '启用' : '禁用'}
                              </span>
                            </td>
                            <td className="py-3 px-4 text-gray-300">{voice.sort_order}</td>
                            <td className="py-3 px-4">
                              <div className="flex space-x-2">
                                {voice.voice_type === 'system' && (
                                  <>
                                    <button
                                      onClick={() => startEdit(voice)}
                                      className="p-1 text-gray-400 hover:text-blue-400 transition-colors"
                                      title="编辑"
                                    >
                                      <Edit className="h-4 w-4" />
                                    </button>
                                    {voice.is_active ? (
                                      <button
                                        onClick={() => handleDeleteVoice(voice.id)}
                                        className="p-1 text-gray-400 hover:text-red-400 transition-colors"
                                        title="禁用"
                                      >
                                        <EyeOff className="h-4 w-4" />
                                      </button>
                                    ) : (
                                      <button
                                        onClick={() => handleRestoreVoice(voice.id)}
                                        className="p-1 text-gray-400 hover:text-green-400 transition-colors"
                                        title="启用"
                                      >
                                        <Eye className="h-4 w-4" />
                                      </button>
                                    )}
                                  </>
                                )}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </Card>
            </div>
          )}

          {/* 用户管理页面 */}
          {activeTab === 'users' && (
            <Card glass>
              <div className="p-6">
                <h3 className="text-lg font-semibold text-white mb-6">用户管理</h3>
                
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="text-left py-3 px-4 text-gray-300">用户</th>
                        <th className="text-left py-3 px-4 text-gray-300">邮箱</th>
                        <th className="text-left py-3 px-4 text-gray-300">公司</th>
                        <th className="text-left py-3 px-4 text-gray-300">管理员</th>
                        <th className="text-left py-3 px-4 text-gray-300">注册时间</th>
                        <th className="text-left py-3 px-4 text-gray-300">操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      {users.map((user) => (
                        <tr key={user.id} className="border-b border-gray-800 hover:bg-gray-800/50">
                          <td className="py-3 px-4">
                            <div className="flex items-center space-x-3">
                              {user.avatar_url ? (
                                <img 
                                  src={user.avatar_url} 
                                  alt={user.name}
                                  className="w-8 h-8 rounded-full"
                                />
                              ) : (
                                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                  <span className="text-white text-sm font-medium">
                                    {user.name.charAt(0).toUpperCase()}
                                  </span>
                                </div>
                              )}
                              <span className="text-white font-medium">{user.name}</span>
                            </div>
                          </td>
                          <td className="py-3 px-4 text-gray-300">{user.email}</td>
                          <td className="py-3 px-4 text-gray-300">{user.company || '-'}</td>
                          <td className="py-3 px-4">
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              user.is_admin 
                                ? 'bg-purple-500/20 text-purple-400' 
                                : 'bg-gray-500/20 text-gray-400'
                            }`}>
                              {user.is_admin ? '是' : '否'}
                            </span>
                          </td>
                          <td className="py-3 px-4 text-gray-300">
                            {new Date(user.created_at).toLocaleDateString('zh-CN')}
                          </td>
                          <td className="py-3 px-4">
                            <button
                              onClick={() => handleToggleUserAdmin(user.id, user.is_admin)}
                              className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                                user.is_admin
                                  ? 'bg-red-500/20 text-red-400 hover:bg-red-500/30'
                                  : 'bg-purple-500/20 text-purple-400 hover:bg-purple-500/30'
                              }`}
                            >
                              {user.is_admin ? '取消管理员' : '设为管理员'}
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};
