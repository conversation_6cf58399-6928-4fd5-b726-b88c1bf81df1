import { jwtDecode } from 'jwt-decode';
import { supabase } from '../lib/supabase';
import type { Voice } from './voiceService';

export interface AdminVoiceStats {
  total_voices: number;
  system_voices: number;
  user_voices: number;
  active_voices: number;
}

export interface AdminUserStats {
  total_users: number;
  active_users: number;
  admin_users: number;
}

export interface CreateSystemVoiceRequest {
  name: string;
  description?: string;
  uri: string;
  model?: string;
  gender?: 'male' | 'female' | 'neutral';
  language?: string;
  preview_text?: string;
  sort_order?: number;
  metadata?: any;
}

export class AdminVoiceService {
  /**
   * 检查用户是否为管理员（从JWT中读取）
   * 注意：这个方法现在主要用于客户端验证，实际的权限检查在服务端通过RLS策略进行
   */
  static checkAdminPermissionFromJWT(accessToken?: string): boolean {
    if (!accessToken) return false;

    try {
      const jwt = jwtDecode<any>(accessToken);
      return jwt.app_metadata?.admin === 'true';
    } catch (error) {
      console.error('Error decoding JWT for admin check:', error);
      return false;
    }
  }

  /**
   * 获取所有音色（管理员视图）
   */
  static async getAllVoicesAdmin(): Promise<Voice[]> {
    try {
      const { data, error } = await supabase
        .from('voices')
        .select('*')
        .order('voice_type')
        .order('sort_order')
        .order('created_at');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching all voices for admin:', error);
      throw error;
    }
  }

  /**
   * 创建系统音色
   */
  static async createSystemVoice(voiceData: CreateSystemVoiceRequest): Promise<Voice> {
    try {
      const { data, error } = await supabase
        .from('voices')
        .insert({
          ...voiceData,
          voice_type: 'system',
          model: voiceData.model || 'FunAudioLLM/CosyVoice2-0.5B',
          language: voiceData.language || 'zh-CN',
          sort_order: voiceData.sort_order || 0,
          is_active: true,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating system voice:', error);
      throw error;
    }
  }

  /**
   * 更新系统音色
   */
  static async updateSystemVoice(
    voiceId: string,
    updates: Partial<CreateSystemVoiceRequest>
  ): Promise<Voice> {
    try {
      const { data, error } = await supabase
        .from('voices')
        .update(updates)
        .eq('id', voiceId)
        .eq('voice_type', 'system')
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating system voice:', error);
      throw error;
    }
  }

  /**
   * 删除系统音色（软删除）
   */
  static async deleteSystemVoice(voiceId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('voices')
        .update({ is_active: false })
        .eq('id', voiceId)
        .eq('voice_type', 'system');

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting system voice:', error);
      throw error;
    }
  }

  /**
   * 永久删除系统音色
   */
  static async permanentDeleteSystemVoice(voiceId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('voices')
        .delete()
        .eq('id', voiceId)
        .eq('voice_type', 'system');

      if (error) throw error;
    } catch (error) {
      console.error('Error permanently deleting system voice:', error);
      throw error;
    }
  }

  /**
   * 恢复已删除的系统音色
   */
  static async restoreSystemVoice(voiceId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('voices')
        .update({ is_active: true })
        .eq('id', voiceId)
        .eq('voice_type', 'system');

      if (error) throw error;
    } catch (error) {
      console.error('Error restoring system voice:', error);
      throw error;
    }
  }

  /**
   * 批量更新音色排序
   */
  static async updateVoiceOrder(voiceOrders: { id: string; sort_order: number }[]): Promise<void> {
    try {
      const updates = voiceOrders.map(({ id, sort_order }) =>
        supabase
          .from('voices')
          .update({ sort_order })
          .eq('id', id)
      );

      await Promise.all(updates);
    } catch (error) {
      console.error('Error updating voice order:', error);
      throw error;
    }
  }

  /**
   * 获取音色统计信息
   */
  static async getVoiceStats(): Promise<AdminVoiceStats> {
    try {
      const { data, error } = await supabase.rpc('get_admin_voice_stats');

      if (error) throw error;
      return data[0] || {
        total_voices: 0,
        system_voices: 0,
        user_voices: 0,
        active_voices: 0,
      };
    } catch (error) {
      console.error('Error fetching voice stats:', error);
      throw error;
    }
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStats(): Promise<AdminUserStats> {
    try {
      const { data, error } = await supabase.rpc('get_admin_user_stats');

      if (error) throw error;
      return data[0] || {
        total_users: 0,
        active_users: 0,
        admin_users: 0,
      };
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw error;
    }
  }

  /**
   * 获取所有用户列表（管理员专用）
   */
  static async getAllUsers(): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching all users:', error);
      throw error;
    }
  }

  /**
   * 切换用户管理员状态
   */
  static async toggleUserAdmin(userId: string, isAdmin: boolean): Promise<void> {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ is_admin: isAdmin })
        .eq('id', userId);

      if (error) throw error;
    } catch (error) {
      console.error('Error toggling user admin status:', error);
      throw error;
    }
  }
}
